import { useState } from "react";
import { Task } from "../types/Task";
import {
  TaskActions,
  TaskProgressCircle,
  TaskTimeDisplay,
  TaskPlayButton,
  SubTaskContainer,
} from "@/components";
import { href, useNavigate } from "react-router";
import {
  getTaskIndentClass,
  getTaskGridClasses,
  getTaskContainerClasses,
  getTaskContentClasses,
  getTaskInfoClasses,
  getTaskTitleClasses,
  getTaskSubtitleClasses,
  getActionButtonClasses,
  getMobileGridClasses,
  isInteractiveElement,
} from "../utils/tailwindUtils";

interface TopLevelTaskProps {
  task: Task;
  level: number;
}

function TopLevelTask({ task, level }: TopLevelTaskProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();

  const handleCardClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!isInteractiveElement(target)) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <article className={getTaskIndentClass(level)}>
      <div className={getTaskContainerClasses()}>
        <header
          className={`${getTaskGridClasses(true)} ${getMobileGridClasses()} bg-card`}
          onClick={handleCardClick}
          onKeyDown={handleKeyDown}
          role="button"
          tabIndex={0}
          aria-expanded={isExpanded}
          aria-label={`Task: ${task.title}. Click to ${isExpanded ? "collapse" : "expand"} subtasks`}
        >
          <div className={getTaskContentClasses()}>
            <TaskProgressCircle
              taskId={task._id}
              completionPercentage={task.completionPercentage ?? 0}
              size="lg"
            />
            <div className={getTaskInfoClasses()}>
              <h3 className={getTaskTitleClasses(false)}>{task.title}</h3>
              <TaskTimeDisplay
                timeSpent={task.timeSpent}
                className={getTaskSubtitleClasses()}
              />
            </div>
          </div>

          <div
            className={`${getActionButtonClasses()} max-sm:justify-self-end`}
          >
            <TaskActions task={task} showDelete={false} />
            <TaskPlayButton
              task={task}
              onPlay={(task) => {
                navigate(
                  href("/tasks/:id", {
                    id: task._id,
                  }),
                );
              }}
            />
          </div>
        </header>

        <SubTaskContainer
          parentTask={task}
          level={level}
          isExpanded={isExpanded}
        />
      </div>
    </article>
  );
}

export default TopLevelTask;
