import { useState } from "react";
import { Task } from "../types/Task";
import {
  TaskActions,
  TaskProgressCircle,
  TaskTimeDisplay,
  TaskPlayButton,
  SubTaskContainer,
} from "@/components";
import { href, useNavigate } from "react-router";
import { calculateTaskPadding, isInteractiveElement } from "../utils/taskUtils";

interface TopLevelTaskProps {
  task: Task;
  level: number;
}

function TopLevelTask({ task, level }: TopLevelTaskProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();
  const paddingLeft = calculateTaskPadding(level, true);

  const handleCardClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!isInteractiveElement(target)) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto rounded-lg border-2">
      <div
        className="w-full px-4 py-3 hover:bg-muted/50 transition-all duration-200 cursor-pointer relative bg-card"
        style={{ marginLeft: `${paddingLeft}rem` }}
        onClick={handleCardClick}
      >
        <div className="flex items-center">
          <div className="flex items-center gap-4 flex-1 pr-24">
            <TaskProgressCircle
              taskId={task._id}
              completionPercentage={task.completionPercentage ?? 0}
              size="lg"
            />
            <div>
              <h3 className="text-lg font-semibold text-foreground">
                {task.title}
              </h3>
              <TaskTimeDisplay
                timeSpent={task.timeSpent}
                className="text-sm text-muted-foreground"
              />
            </div>
          </div>
          <div className="absolute right-4 flex items-center gap-2">
            <TaskActions task={task} showDelete={false} />
            <TaskPlayButton
              task={task}
              onPlay={(task) => {
                navigate(
                  href("/tasks/:id", {
                    id: task._id,
                  }),
                );
              }}
            />
          </div>
        </div>
      </div>

      <SubTaskContainer
        parentTask={task}
        level={level}
        isExpanded={isExpanded}
      />
    </div>
  );
}

export default TopLevelTask;
