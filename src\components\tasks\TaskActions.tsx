import { useAdminMode } from "@/components/providers/AdminModeProvider";
import { Task } from "../types/Task";
import { TaskEditDialog } from "../components/TaskEditDialog";
import { TaskDeleteDialog } from "../components/TaskDeleteDialog";
import { Dialog } from "@/components/ui/dialog";

interface TaskActionsProps {
  task: Task;
  showDelete: boolean;
}

/**
 * REFACTORED TaskActions component
 * - Split into focused, single-responsibility components
 * - Much smaller and easier to maintain
 * - Better separation of concerns
 * - Reusable dialog components
 */
function TaskActions({ task, showDelete }: TaskActionsProps) {
  const { mode } = useAdminMode();

  if (mode === "OFF") return null;

  return (
    <div className="flex gap-1">
      <Dialog>
        <TaskEditDialog task={task} />
      </Dialog>

      {showDelete && <TaskDeleteDialog task={task} />}
    </div>
  );
}

export default TaskActions;
