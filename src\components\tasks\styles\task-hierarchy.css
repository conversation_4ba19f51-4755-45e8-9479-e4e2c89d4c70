/**
 * Task Hierarchy CSS Architecture
 * Modern CSS Grid/Flexbox implementation replacing absolute positioning
 */

:root {
  /* Task Hierarchy Variables */
  --task-level-base: 1rem;
  --task-level-multiplier: 1;
  --task-top-level-multiplier: 1.5;
  --task-max-width: 64rem; /* 4xl */
  --task-border-radius: 0.5rem;
  --task-transition: all 0.2s ease-in-out;
  
  /* Task Spacing */
  --task-padding-x: 1rem;
  --task-padding-y: 0.75rem;
  --task-gap: 0.5rem;
  --task-action-gap: 0.5rem;
  
  /* Task Colors */
  --task-bg: rgb(15 23 42); /* slate-900 */
  --task-bg-hover: rgb(30 41 59 / 0.3); /* gray-800/30 */
  --task-border: rgb(148 163 184 / 0.2); /* slate-400/20 */
  --task-text-primary: rgb(248 250 252); /* slate-50 */
  --task-text-secondary: rgb(156 163 175); /* gray-400 */
  
  /* Hierarchy Line */
  --hierarchy-line-width: 2px;
  --hierarchy-line-color: rgb(51 65 85); /* slate-700 */
  --hierarchy-line-offset: calc(var(--task-level-base) + 0.5rem);
  
  /* Responsive Breakpoints */
  --mobile-max: 640px;
  --tablet-max: 1024px;
}

/* Task Container Base */
.task-container {
  width: 100%;
  max-width: var(--task-max-width);
  margin: 0 auto;
  border-radius: var(--task-border-radius);
  border: 2px solid var(--task-border);
  overflow: hidden;
}

/* Task Grid Layout */
.task-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  grid-template-areas: "content actions";
  align-items: center;
  gap: var(--task-gap);
  padding: var(--task-padding-y) var(--task-padding-x);
  background-color: var(--task-bg);
  transition: var(--task-transition);
  cursor: pointer;
  position: relative;
}

.task-grid:hover {
  background-color: var(--task-bg-hover);
}

.task-grid--no-hover {
  cursor: default;
}

.task-grid--no-hover:hover {
  background-color: var(--task-bg);
}

/* Task Content Area */
.task-content {
  grid-area: content;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 0; /* Allow flex items to shrink */
}

.task-info {
  flex: 1;
  min-width: 0; /* Allow text to truncate */
}

.task-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--task-text-primary);
  margin-bottom: 0.25rem;
  word-wrap: break-word;
  hyphens: auto;
}

.task-subtitle {
  font-size: 0.875rem;
  color: var(--task-text-secondary);
}

/* Task Actions Area */
.task-actions {
  grid-area: actions;
  display: flex;
  align-items: center;
  gap: var(--task-action-gap);
  flex-shrink: 0;
}

/* Hierarchy Indentation */
.task-hierarchy {
  position: relative;
}

.task-hierarchy[data-level="0"] {
  margin-left: 0;
}

.task-hierarchy[data-level="1"] {
  margin-left: calc(var(--task-level-base) * var(--task-top-level-multiplier));
}

.task-hierarchy[data-level="2"] {
  margin-left: calc(var(--task-level-base) * var(--task-top-level-multiplier) * 2);
}

.task-hierarchy[data-level="3"] {
  margin-left: calc(var(--task-level-base) * var(--task-top-level-multiplier) * 3);
}

.task-hierarchy[data-level="4"] {
  margin-left: calc(var(--task-level-base) * var(--task-top-level-multiplier) * 4);
}

.task-hierarchy[data-level="5"] {
  margin-left: calc(var(--task-level-base) * var(--task-top-level-multiplier) * 5);
}

/* Subtask Specific Styles */
.subtask-container {
  position: relative;
  width: 100%;
}

.subtask-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  grid-template-areas: "content actions";
  align-items: flex-start;
  gap: var(--task-gap);
  padding: var(--task-padding-y) var(--task-padding-x);
  transition: var(--task-transition);
  cursor: pointer;
}

.subtask-grid:hover {
  background-color: var(--task-bg-hover);
}

.subtask-content {
  grid-area: content;
  flex: 1;
  min-width: 0;
}

.subtask-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.subtask-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--task-text-primary);
  word-wrap: break-word;
  hyphens: auto;
}

.subtask-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.subtask-progress-bar {
  flex: 1;
  height: 0.5rem;
}

.subtask-progress-text {
  font-size: 0.75rem;
  color: var(--task-text-secondary);
  white-space: nowrap;
}

.subtask-actions {
  grid-area: actions;
  display: flex;
  align-items: flex-end;
  gap: var(--task-action-gap);
  flex-shrink: 0;
}

/* Hierarchy Lines */
.hierarchy-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: var(--hierarchy-line-width);
  background-color: var(--hierarchy-line-color);
  left: var(--hierarchy-line-offset);
}

/* Subtask Indentation */
.subtask-hierarchy[data-level="1"] {
  padding-left: calc(var(--task-level-base) * var(--task-level-multiplier));
}

.subtask-hierarchy[data-level="2"] {
  padding-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 2);
}

.subtask-hierarchy[data-level="3"] {
  padding-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 3);
}

.subtask-hierarchy[data-level="4"] {
  padding-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 4);
}

.subtask-hierarchy[data-level="5"] {
  padding-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 5);
}

/* Create Task Button Container */
.create-task-container {
  padding: 0.5rem 1rem;
  position: relative;
}

.create-task-container[data-level="1"] {
  margin-left: calc(var(--task-level-base) * var(--task-level-multiplier));
}

.create-task-container[data-level="2"] {
  margin-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 2);
}

.create-task-container[data-level="3"] {
  margin-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 3);
}

.create-task-container[data-level="4"] {
  margin-left: calc(var(--task-level-base) * var(--task-level-multiplier) * 4);
}

/* Responsive Design */
@media (max-width: 640px) {
  :root {
    --task-level-base: 0.75rem;
    --task-padding-x: 0.75rem;
    --task-padding-y: 0.5rem;
    --task-gap: 0.25rem;
    --task-action-gap: 0.25rem;
  }
  
  .task-container {
    border-radius: 0.25rem;
    border-width: 1px;
  }
  
  .task-title {
    font-size: 1rem;
  }
  
  .task-actions {
    flex-direction: column;
    align-items: flex-end;
  }
  
  .subtask-actions {
    flex-direction: column;
    align-items: flex-end;
  }
  
  /* Reduce indentation on mobile */
  .task-hierarchy[data-level="1"],
  .task-hierarchy[data-level="2"],
  .task-hierarchy[data-level="3"],
  .task-hierarchy[data-level="4"],
  .task-hierarchy[data-level="5"] {
    margin-left: calc(var(--task-level-base) * var(--task-level-multiplier) * var(--level, 1));
  }
}

@media (max-width: 480px) {
  :root {
    --task-level-base: 0.5rem;
  }
  
  .task-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "content"
      "actions";
    gap: 0.5rem;
  }
  
  .task-actions {
    justify-self: end;
    flex-direction: row;
  }
  
  .subtask-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "content"
      "actions";
  }
  
  .subtask-actions {
    justify-self: end;
    flex-direction: row;
  }
}
