import { useState } from "react";
import { Task } from "../types/Task";
import {
  TaskActions,
  TaskProgressCircle,
  TaskTimeDisplay,
  TaskPlayButton,
  SubTaskContainer,
} from "@/components";
import { href, useNavigate } from "react-router";
import { isInteractiveElement } from "../utils/taskUtils";
import "../styles/task-hierarchy.css";

interface TopLevelTaskProps {
  task: Task;
  level: number;
}

/**
 * MODERN TopLevelTask Component
 * - Uses CSS Grid instead of absolute positioning
 * - Responsive design with CSS custom properties
 * - Semantic HTML structure
 * - No inline styles or magic numbers
 */
function TopLevelTaskModern({ task, level }: TopLevelTaskProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();

  const handleCardClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!isInteractiveElement(target)) {
      setIsExpanded(!isExpanded);
    }
  };

  const handlePlayTask = (task: Task) => {
    navigate(href("/tasks/:id", { id: task._id }));
  };

  return (
    <article className="task-hierarchy" data-level={level}>
      <div className="task-container">
        <header 
          className="task-grid"
          onClick={handleCardClick}
          role="button"
          tabIndex={0}
          aria-expanded={isExpanded}
          aria-label={`Task: ${task.title}. Click to ${isExpanded ? 'collapse' : 'expand'} subtasks`}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleCardClick(e as any);
            }
          }}
        >
          <div className="task-content">
            <TaskProgressCircle
              taskId={task._id}
              completionPercentage={task.completionPercentage ?? 0}
              size="lg"
            />
            <div className="task-info">
              <h3 className="task-title">{task.title}</h3>
              <TaskTimeDisplay
                timeSpent={task.timeSpent}
                className="task-subtitle"
              />
            </div>
          </div>
          
          <div className="task-actions">
            <TaskActions task={task} showDelete={false} />
            <TaskPlayButton
              task={task}
              onPlay={handlePlayTask}
            />
          </div>
        </header>

        {/* Expanded Content - Subtasks */}
        <SubTaskContainer
          parentTask={task}
          level={level}
          isExpanded={isExpanded}
        />
      </div>
    </article>
  );
}

export default TopLevelTaskModern;
