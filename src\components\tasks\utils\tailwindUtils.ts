/**
 * Tailwind utility functions for task components
 * Returns appropriate Tailwind classes based on task hierarchy level
 */

/**
 * Get task indentation class for top-level tasks
 */
export function getTaskIndentClass(level: number): string {
  const indentClasses = {
    0: 'task-indent-0',
    1: 'task-indent-1',
    2: 'task-indent-2',
    3: 'task-indent-3',
    4: 'task-indent-4',
    5: 'task-indent-5',
  } as const;
  
  return indentClasses[level as keyof typeof indentClasses] || 'task-indent-0';
}

/**
 * Get subtask indentation class for nested tasks
 */
export function getSubtaskIndentClass(level: number): string {
  const indentClasses = {
    1: 'subtask-indent-1',
    2: 'subtask-indent-2',
    3: 'subtask-indent-3',
    4: 'subtask-indent-4',
    5: 'subtask-indent-5',
  } as const;
  
  return indentClasses[level as keyof typeof indentClasses] || '';
}

/**
 * Get responsive grid classes for task layout
 */
export function getTaskGridClasses(isExpandable = true): string {
  const baseClasses = 'grid grid-cols-[1fr_auto] items-center gap-4 p-4 transition-all duration-200';
  const hoverClasses = isExpandable 
    ? 'cursor-pointer hover:bg-muted/50' 
    : '';
  
  return `${baseClasses} ${hoverClasses}`.trim();
}

/**
 * Get responsive grid classes for subtask layout
 */
export function getSubtaskGridClasses(canExpand = true): string {
  const baseClasses = 'grid grid-cols-[1fr_auto] items-start gap-2 p-3 transition-all duration-200';
  const hoverClasses = canExpand 
    ? 'cursor-pointer hover:bg-muted/30' 
    : '';
  
  return `${baseClasses} ${hoverClasses}`.trim();
}

/**
 * Get action button container classes
 */
export function getActionButtonClasses(): string {
  return 'flex items-center gap-2 sm:gap-1 flex-shrink-0';
}

/**
 * Get task container classes with shadcn styling
 */
export function getTaskContainerClasses(): string {
  return 'w-full max-w-4xl mx-auto rounded-lg border-2 border-border overflow-hidden';
}

/**
 * Get task content area classes
 */
export function getTaskContentClasses(): string {
  return 'flex items-center gap-4 min-w-0 flex-1';
}

/**
 * Get task info classes (title and subtitle area)
 */
export function getTaskInfoClasses(): string {
  return 'flex-1 min-w-0';
}

/**
 * Get task title classes
 */
export function getTaskTitleClasses(isSubtask = false): string {
  const baseClasses = 'font-semibold text-foreground break-words hyphens-auto';
  const sizeClasses = isSubtask ? 'text-sm' : 'text-lg';
  
  return `${baseClasses} ${sizeClasses}`;
}

/**
 * Get task subtitle/time display classes
 */
export function getTaskSubtitleClasses(): string {
  return 'text-sm text-muted-foreground';
}

/**
 * Get responsive classes for mobile layout
 */
export function getMobileGridClasses(): string {
  return 'sm:grid-cols-[1fr_auto] max-sm:grid-cols-1 max-sm:gap-2';
}

/**
 * Get mobile action button classes
 */
export function getMobileActionClasses(): string {
  return 'max-sm:justify-self-end max-sm:flex-row sm:flex-col lg:flex-row';
}

/**
 * Check if element is interactive (for click handling)
 */
export function isInteractiveElement(target: HTMLElement): boolean {
  return !!target.closest('button, [role="button"], input, select, textarea, a');
}

/**
 * Get hierarchy line classes for subtask containers
 */
export function getHierarchyLineClasses(level: number): string {
  if (level === 0) return '';
  
  return 'before:absolute before:top-0 before:bottom-0 before:w-0.5 before:bg-border before:left-6';
}
